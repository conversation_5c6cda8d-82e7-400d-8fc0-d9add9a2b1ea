ALTER PROC dbo.ev_importEventFromQueue
@itemUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @recordedByMemberID int, @siteID int, @orgID int, @defaultTimeZoneID int, @ovAction char(1), 
		@MCEventID int, @MCCalendarID int, @EventAllDay bit, @EventCode varchar(15), @EventHidden bit, 
		@ContactInclude bit, @LocationInclude bit, @CancellationInclude bit, @TravelInclude bit, @EventStart datetime, 
		@EventEnd datetime,	@eventContentID int, @EventTitle varchar(200), @EventSubTitle varchar(200), @EventDescription varchar(max), @MCCategoryIDList varchar(max),
		@origCalendarID int, @InternalNotes varchar(max), @RegistrationReplyEmail varchar(200), @MCRegistrationID int,
		@DisplayCredits bit, @expirationContentID int, @ContactTitle varchar(200), @Contact varchar(max), @contentTitle varchar(200), 
		@rawContent varchar(max), @LocationTitle varchar(200), @Location varchar(max), @CancellationTitle varchar(200), 
		@Cancellation varchar(max), @TravelTitle varchar(200), @Travel varchar(max), @InformationTitle varchar(200), 
		@Information varchar(max), @locationContentID int, @cancellationPolicyContentID int, @travelContentID int,
		@informationContentID int, @subEventID int, @existingEvent bit, @ASID int, @crdAuthorityCode varchar(20),
		@crdApproval varchar(50), @crdStatus varchar(20), @crdOfferedID int, @crdStatusID int, @ASTID int, @crdColName sysname,
		@crdValue decimal(6,2), @contactContentID int, @MCParentEventID int, @ParentEventCode varchar(15), @crdupdated bit, 
		@itemStatus int, @statusReady int, @EnableRealTimeRoster bit, @eventSiteResourceID int, @minFieldID int, 
		@fieldReference varchar(128), @dataTypeCode varchar(20), @displayTypeCode varchar(20), @timeID int, 
		@eventAdminSRID int, @crossEventFieldUsageID int, @fieldValue varchar(max), @dataID int, 
		@RecurringSeriesCode varchar(15), @createdFromEventID int, @recurringSeriesID int, @recurrenceOrder int,
		@recurrenceAFID int, @lockTimeZoneID int, @datePart varchar(20), @dateNum int, @adjustTerm varchar(12), 
		@nextWeekday int, @weekNumber varchar(4), @defaultTimeID int, @lockedTimeID int, @isRecurringEvent bit = 0;
	declare @tblPossibleCredits TABLE (ASID int, authorityID int, authorityCode varchar(20));
	declare @tblPossibleCreditCols TABLE (ASID int, column_name sysname, ASTID int);
	declare @tblCrossEventFields TABLE (fieldID int, fieldReference varchar(128), displayTypeCode varchar(20), dataTypeCode varchar(20));

	-- if itemUID is not readyToProcess, kick out now
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'importEvents'
		and qs.queueStatus = 'readyToProcess';
	select @itemStatus = queueStatusID
		from platformQueue.dbo.tblQueueItems
		where itemUID = @itemUID;
	IF @itemStatus is null
		RETURN 0;
	IF @itemStatus <> @statusReady
		RETURN 0;

	-- update status
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='processingEvent';

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData;

	select qid.columnID, dc.columnname, qid.columnValueString, qid.columnValueDecimal2, qid.columnValueInteger, qid.columnvalueDate, 
		qid.columnValueBit, qid.columnValueText
	into #tmpEVQueueData
	from platformQueue.dbo.tblQueueItemData as qid
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	where qid.itemUID = @itemUID;

	select top 1 @recordedByMemberID=recordedByMemberID, @siteID=siteID
	from platformQueue.dbo.tblQueueItemData
	where itemUID = @itemUID;

	select @orgID=orgID, @defaultTimeZoneID=defaultTimeZoneID from dbo.sites where siteID=@siteID;
	select @ovAction = columnValueString from #tmpEVQueueData where columnname = 'ovAction';
	select @MCEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCEventID';
	select @MCRegistrationID = columnValueInteger from #tmpEVQueueData where columnname = 'MCRegistrationID';
	select @EventStart = columnValueDate from #tmpEVQueueData where columnname = 'EventStart';
	select @EventEnd = columnValueDate from #tmpEVQueueData where columnname = 'EventEnd';

	IF @MCEventID is not null
		set @existingEvent = 1;
	ELSE
		set @existingEvent = 0;

	IF @existingEvent = 1 AND EXISTS (SELECT 1 FROM dbo.ev_events WHERE eventID = @MCEventID AND siteID = @siteID AND recurringSeriesID IS NOT NULL)
		SET @isRecurringEvent = 1;
	ELSE IF @existingEvent = 0 BEGIN
		select @RecurringSeriesCode = isnull((select columnValueString from #tmpEVQueueData where columnname = 'RecurringSeriesCode'),'');

		IF LEN(@RecurringSeriesCode) > 0
			SET @isRecurringEvent = 1;
	END
	
	BEGIN TRAN;
		-- if we are updating event with overwrite setting
		IF @existingEvent = 1 and @ovAction = 'o' BEGIN
			select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID';
			select @MCCategoryIDList = replace(columnValueText,'|',',') from #tmpEVQueueData where columnname = 'MCCategoryIDList';
			select @origCalendarID = calendarID from dbo.ev_calendarEvents where sourceEventID = @MCEventID and calendarID = sourceCalendarID;

			-- move to diff calendar if necessary (this also moves categories)
			IF (@MCCalendarID != @origCalendarID) BEGIN
				EXEC dbo.ev_moveCalendarEvent @eventID=@MCEventID, @fromCalendarID=@origCalendarID, @toCalendarID=@MCCalendarID, 
					@toCategoryIDList=@MCCategoryIDList, @recordedByMemberID=@recordedByMemberID;
			END

			-- set new category
			IF (@MCCalendarID = @origCalendarID) BEGIN
				delete from dbo.ev_eventCategories
				where eventID = @MCEventID;

				INSERT INTO dbo.ev_eventCategories (eventID, categoryID, categoryOrder)
				SELECT @MCEventID, li.listItem, li.autoID
				from dbo.fn_intListToTable(@MCCategoryIDList,',') as li;
			END

			-- update changed fields
			select @EventAllDay = columnValueBit from #tmpEVQueueData where columnname = 'EventAllDay';
			select @EventHidden = columnValueBit from #tmpEVQueueData where columnname = 'EventHidden';
			select @ContactInclude = columnValueBit from #tmpEVQueueData where columnname = 'ContactInclude';
			select @LocationInclude = columnValueBit from #tmpEVQueueData where columnname = 'LocationInclude';
			select @CancellationInclude = columnValueBit from #tmpEVQueueData where columnname = 'CancellationInclude';
			select @TravelInclude = columnValueBit from #tmpEVQueueData where columnname = 'TravelInclude';
			select @InternalNotes = columnValueText from #tmpEVQueueData where columnname = 'InternalNotes';
			select @EventSubTitle = columnValueString from #tmpEVQueueData where columnname = 'EventSubTitle';

			--determine if times need to be forced to full day, if allDayEvent
			select @EventAllDay = isnull(@EventAllDay,isAllDayEvent)
			from dbo.ev_events
			where eventID = @MCEventID;

			if (@EventAllDay = 1) BEGIN
				set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart));
				set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)));
			END

			update dbo.ev_events
			set isAllDayEvent = @EventAllDay,
				hiddenFromCalendar = isnull(@EventHidden,hiddenFromCalendar),
				emailContactContent = isnull(@ContactInclude,emailContactContent),
				emailLocationContent = isnull(@LocationInclude,emailLocationContent),
				emailCancelContent = isnull(@CancellationInclude,emailCancelContent),
				emailTravelContent = isnull(@TravelInclude,emailTravelContent),
				internalNotes = isnull(@InternalNotes,internalNotes),
				eventSubTitle = isnull(@EventSubTitle,eventSubTitle)
			where eventID = @MCEventID;

			-- update event times
			IF @isRecurringEvent = 0 BEGIN
				UPDATE dbo.ev_events set defaultTimeID = null, lockedTimeID = null WHERE eventID = @MCEventID;
				delete from dbo.ev_times where eventID = @MCEventID;
				EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@defaultTimeZoneID, @startTime=@EventStart, @endTime=@EventEnd, @timeID=@timeID OUTPUT;
			END

			-- update title/description
			select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle';
			select @EventDescription = columnValueText from #tmpEVQueueData where columnname = 'EventDescription';

			select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@eventContentID,1);
			set @EventDescription = isnull(@EventDescription,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isHTML=1, 
				@contentTitle=@EventTitle, @contentDesc='', @rawcontent=@EventDescription;

			-- update other content objects
			select @ContactTitle = columnValueString from #tmpEVQueueData where columnname = 'ContactTitle';
			select @Contact = columnValueText from #tmpEVQueueData where columnname = 'Contact';
			select @LocationTitle = columnValueString from #tmpEVQueueData where columnname = 'LocationTitle';
			select @Location = columnValueText from #tmpEVQueueData where columnname = 'Location';
			select @CancellationTitle = columnValueString from #tmpEVQueueData where columnname = 'CancellationTitle';
			select @Cancellation = columnValueText from #tmpEVQueueData where columnname = 'Cancellation';
			select @TravelTitle = columnValueString from #tmpEVQueueData where columnname = 'TravelTitle';
			select @Travel = columnValueText from #tmpEVQueueData where columnname = 'Travel';
			select @InformationTitle = columnValueString from #tmpEVQueueData where columnname = 'InformationTitle';
			select @Information = columnValueText from #tmpEVQueueData where columnname = 'Information';

			select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@contactContentID,1);
			set @ContactTitle = isnull(@ContactTitle,@contentTitle);
			set @Contact = isnull(@Contact,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isHTML=1, @contentTitle=@ContactTitle, 
				@contentDesc='', @rawcontent=@Contact;

			select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@locationContentID,1);
			set @LocationTitle = isnull(@LocationTitle,@contentTitle);
			set @Location = isnull(@Location,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isHTML=1, @contentTitle=@LocationTitle, 
				@contentDesc='', @rawcontent=@Location;

			select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@cancellationPolicyContentID,1);
			set @CancellationTitle = isnull(@CancellationTitle,@contentTitle);
			set @Cancellation = isnull(@Cancellation,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isHTML=1, @contentTitle=@CancellationTitle, 
				@contentDesc='', @rawcontent=@Cancellation;

			select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@travelContentID,1);
			set @TravelTitle = isnull(@TravelTitle,@contentTitle);
			set @Travel = isnull(@Travel,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isHTML=1, @contentTitle=@TravelTitle, 
				@contentDesc='', @rawcontent=@Travel;

			select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@informationContentID,1);
			set @InformationTitle = isnull(@InformationTitle,@contentTitle);
			set @Information = isnull(@Information,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isHTML=1, @contentTitle=@InformationTitle, 
				@contentDesc='', @rawcontent=@Information;
		END

		-- if we are adding new event
		IF @existingEvent = 0 BEGIN
			select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID';
			select @MCCategoryIDList = columnValueText from #tmpEVQueueData where columnname = 'MCCategoryIDList';
			select @EventAllDay = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'EventAllDay'),0);
			select @EventCode = columnValueString from #tmpEVQueueData where columnname = 'EventCode';
			select @EventHidden = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'EventHidden'),0);
			select @ContactInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'ContactInclude'),0);
			select @LocationInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'LocationInclude'),0);
			select @CancellationInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'CancellationInclude'),0);
			select @TravelInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'TravelInclude'),0);
			select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle';
			select @EventSubTitle = columnValueString from #tmpEVQueueData where columnname = 'EventSubTitle';
			select @EventDescription = isnull((select columnValueText from #tmpEVQueueData where columnname = 'EventDescription'),'');
			select @InternalNotes = isnull((select columnValueText from #tmpEVQueueData where columnname = 'InternalNotes'),'');
			select @ContactTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'ContactTitle'),'');
			select @Contact = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Contact'),'');
			select @LocationTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'LocationTitle'),'');
			select @Location = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Location'),'');
			select @CancellationTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'CancellationTitle'),'');
			select @Cancellation = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Cancellation'),'');
			select @TravelTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'TravelTitle'),'');
			select @Travel = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Travel'),'');
			select @InformationTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'InformationTitle'),'');
			select @Information = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Information'),'');

			--determine if times need to be forced to full day, if allDayEvent
			if (@EventAllDay = 1) BEGIN
				set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart));
				set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)));
			END

			EXEC dbo.ev_createEvent @siteID=@siteID, @calendarid=@MCCalendarID, @eventTypeID=1, @enteredByMemberID=@recordedByMemberID, 
				@eventSubTitle=@EventSubTitle, @lockTimeZoneID=null, @isAllDayEvent=@EventAllDay, @altRegistrationURL=null, @status='A', 
				@reportCode=@EventCode, @hiddenFromCalendar=@EventHidden, @emailContactContent=@ContactInclude, 
				@emailLocationContent=@LocationInclude, @emailCancelContent=@CancellationInclude, @emailTravelContent=@TravelInclude, 
				@eventID=@MCEventID OUTPUT;
			EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@defaultTimeZoneID, @startTime=@EventStart, @endTime=@EventEnd, @timeID=@timeID OUTPUT;

			IF len(@InternalNotes) > 0
				update dbo.ev_events
				set internalNotes = @InternalNotes
				where eventID = @MCEventID;

			INSERT INTO dbo.ev_eventCategories (eventID, categoryID, categoryOrder)
			SELECT @MCEventID, li.listItem, li.autoID
			from dbo.fn_intListToTable(@MCCategoryIDList,'|') as li;

			select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isHTML=1, @contentTitle=@EventTitle, 
				@contentDesc='', @rawcontent=@EventDescription;

			IF LEN(@ContactTitle) > 0 or LEN(@Contact) > 0 BEGIN
				select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isHTML=1, @contentTitle=@ContactTitle, 
					@contentDesc='', @rawcontent=@Contact;
			END

			IF LEN(@LocationTitle) > 0 or LEN(@Location) > 0 BEGIN
				select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isHTML=1, @contentTitle=@LocationTitle, 
					@contentDesc='', @rawcontent=@Location;
			END

			IF LEN(@CancellationTitle) > 0 or LEN(@Cancellation) > 0 BEGIN
				select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isHTML=1, @contentTitle=@CancellationTitle, 
					@contentDesc='', @rawcontent=@Cancellation;
			END

			IF LEN(@TravelTitle) > 0 or LEN(@Travel) > 0 BEGIN
				select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isHTML=1, @contentTitle=@TravelTitle, 
					@contentDesc='', @rawcontent=@Travel;
			END

			IF LEN(@InformationTitle) > 0 or LEN(@Information) > 0 BEGIN
				select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isHTML=1, @contentTitle=@InformationTitle, 
					@contentDesc='', @rawcontent=@Information;
			END
		END 

		-- if we are updating registration with overwrite setting
		IF @existingEvent = 1 and @MCRegistrationID is not null and @ovAction = 'o' BEGIN
			select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail';
			select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits';
			select @EnableRealTimeRoster = columnValueBit from #tmpEVQueueData where columnname = 'EnableRealTimeRoster';

			update dbo.ev_registration
			set replyToEmail = @RegistrationReplyEmail,
				showCredit = isnull(@DisplayCredits,showCredit),
				enableRealTimeRoster = isnull(@EnableRealTimeRoster,enableRealTimeRoster)
			where registrationID = @MCRegistrationID;
		END

		-- if event does not have registration
		IF @MCRegistrationID is null AND @isRecurringEvent = 0 BEGIN
			select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail';
			select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits';
			select @EnableRealTimeRoster = columnValueBit from #tmpEVQueueData where columnname = 'EnableRealTimeRoster';

			-- it could have rsvp or alt reg, so we need to switch it
			IF EXISTS(select registrationID from dbo.ev_registration where eventID = @MCEventID and status = 'A' and siteID = @siteID)
				UPDATE dbo.ev_registration
				SET status = 'D'
				WHERE eventID = @MCEventID 
				AND siteID = @siteID
				and status = 'A';

			UPDATE dbo.ev_events
			SET altRegistrationURL = NULL
			WHERE eventID = @MCEventID
			AND altRegistrationURL is not null;

			EXEC dbo.ev_createRegistration @eventID=@MCEventID, @registrationTypeID=1, @startDate=@EventStart, @endDate=@EventEnd,
				@registrantCap=null, @replyToEmail=@RegistrationReplyEmail, @notifyEmail='', @isPriceBasedOnActual=1, @bulkCountByRate=0,
				@registrationID=@MCRegistrationID OUTPUT;

			update dbo.ev_registration
			set showCredit = isnull(@DisplayCredits,1),
				enableRealTimeRoster = isnull(@EnableRealTimeRoster,enableRealTimeRoster)
			where registrationID = @MCRegistrationID;

			select @expirationContentID = expirationContentID FROM dbo.ev_registration WHERE registrationID = @MCRegistrationID;
			EXEC dbo.cms_updateContent @contentID=@expirationContentID, @languageID=1, @isHTML=1, 
				@contentTitle='Expiration Message', @contentDesc='', @rawcontent='Registration for this event has closed.';
		END

		-- handle sub events
		IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
			select @MCParentEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCParentEventID';
			select @ParentEventCode = nullIf(columnValueString,'') from #tmpEVQueueData where columnname = 'ParentEventCode';

			-- if @MCParentEventID is null but @ParentEventCode is not null, this event should be a child of a recently added event
			IF @MCParentEventID is null and @ParentEventCode is not null BEGIN
				select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID';

				select @MCParentEventID = e.eventID
				from dbo.ev_events as e
				inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
					and ce.calendarID = @MCCalendarID
					and ce.calendarID = ce.sourceCalendarID
				where e.reportCode = @ParentEventCode
				and e.status = 'A'
				and e.siteID = @siteID;
			END

			-- if @MCParentEventID is not null, this event should be a child of that event	
			IF @MCParentEventID is not null BEGIN
				select @subEventID = subEventID from dbo.ev_subEvents where parentEventID=@MCParentEventID and eventID=@MCEventID;
				IF @subEventID is null 
					insert into dbo.ev_subEvents (parentEventID, eventID)
					values (@MCParentEventID, @MCEventID);
			END
		END

		-- credits
		IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
			insert into @tblPossibleCredits (ASID, authorityID, authorityCode)
			select distinct crdAS.ASID, crdA.authorityID, crdA.authorityCode
			from dbo.crd_sponsors as crdS
			inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
			inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
			where crdS.orgID = @orgID;

			insert into @tblPossibleCreditCols (ASID, column_name, ASTID)
			select crdAS.ASID, crdAS.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID 
			from @tblPossibleCredits as crdAS
			inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdAS.authorityID
			inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID;

			select @ASID = min(ASID) from @tblPossibleCredits;
			while @ASID is not null begin
				select @crdAuthorityCode=null, @crdApproval=null, @crdStatus=null, @crdOfferedID=null, @crdStatusID=null, @ASTID=null;

				select @crdAuthorityCode = authorityCode from @tblPossibleCredits where ASID = @ASID;
				select @crdApproval = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_approval';
				select @crdStatus = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_status';

				IF @crdApproval is not null and @crdStatus is not null BEGIN
					select @crdOfferedID = offeringID from dbo.crd_offerings where ASID = @ASID and eventID = @MCEventID;
					IF @crdOfferedID is null
						EXEC dbo.crd_addOffering @applicationType='Events', @itemID=@MCEventID, @ASID=@ASID, @offeredID=@crdOfferedID OUTPUT;

					select @crdStatusID = statusID from dbo.crd_statuses where [status] = @crdStatus;

					UPDATE dbo.crd_offerings
					SET statusID = @crdStatusID, approvalNum = @crdApproval
					WHERE offeringID = @crdOfferedID;

					select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID;
					while @ASTID is not null begin
						select @crdColName=null, @crdValue=null;

						select @crdColName = column_name from @tblPossibleCreditCols where ASTID = @ASTID;
						select @crdValue = columnValueDecimal2 from #tmpEVQueueData where columnname = @crdColName;

						IF @crdValue is not null
							EXEC dbo.crd_updateOfferingType @offeringID=@crdOfferedID, @ASTID=@ASTID, @creditValue=@crdValue, @updated=@crdupdated OUTPUT;

						select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID and ASTID > @ASTID;
					end
				END

				select @ASID = min(ASID) from @tblPossibleCredits where ASID > @ASID;
			end
		END

		-- cross-event field data
		IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
			select @eventAdminSRID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin',@siteID);
			select @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',null);

			insert into @tblCrossEventFields (fieldID, fieldReference, displayTypeCode, dataTypeCode)
			select f.fieldID, f.fieldReference, ft.displayTypeCode, ft.dataTypeCode
			from dbo.cf_fields as f
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			where fu.usageID = @crossEventFieldUsageID
			and f.controllingSiteResourceID = @eventAdminSRID
			and len(f.fieldReference) > 0
			and f.isActive = 1
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
						else 1 end
			order by f.fieldOrder;

			select @eventSiteResourceID = siteResourceID from dbo.ev_events where eventID = @MCEventID;
			IF @eventSiteResourceID IS NOT NULL BEGIN
				DELETE FROM dbo.cf_fieldData
				WHERE itemID = @eventSiteResourceID
				AND itemType = 'CrossEvent';
			
				select @minFieldID = min(fieldID) from @tblCrossEventFields;
				while @minFieldID is not null BEGIN
					select @fieldReference=null, @displayTypeCode=null, @dataTypeCode=null;

					select @fieldReference = fieldReference, @displayTypeCode = displayTypeCode, @dataTypeCode = dataTypeCode 
					from @tblCrossEventFields 
					where fieldID = @minFieldID;

					IF @displayTypeCode in ('SELECT','RADIO','CHECKBOX') BEGIN
						INSERT INTO dbo.cf_fieldData (fieldID, itemType, itemID, valueID, amount)
						select f.fieldID, 'CrossEvent', @eventSiteResourceID, fv.valueID, 0 as amount
						from #tmpEVQueueData as tmp
						inner join @tblCrossEventFields as cevf on cevf.fieldReference = tmp.columnname
						inner join dbo.cf_fields as f on f.fieldID = cevf.fieldID
							and f.fieldID = @minFieldID
						inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
						inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
							and case when ft.dataTypeCode = 'DATE' then cast(cast(tmp.columnValueString as date) as varchar(15)) else tmp.columnValueString end
								 = case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
										 when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
										 when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(10))
										 when ft.dataTypeCode = 'BIT' then cast(fv.valueBit as varchar(1))
										 when ft.dataTypeCode = 'DATE' then cast(fv.valueDate as varchar(15))
									else '' end;
					END
					ELSE BEGIN
						select @fieldValue = null, @dataID = null;

						select @fieldValue = columnValueString
						from #tmpEVQueueData as tmp
						inner join @tblCrossEventFields as cevf on cevf.fieldReference = tmp.columnname
						inner join dbo.cf_fields as f on f.fieldID = cevf.fieldID
						where f.fieldID = @minFieldID;

						IF @fieldValue IS NOT NULL
							EXEC dbo.cf_setFieldData @fieldID=@minFieldID, @itemID=@eventSiteResourceID, @itemType='CrossEvent', 
								@valueID=NULL, @fieldValue=@fieldValue, @dataID=@dataID OUTPUT;
					END

					select @minFieldID = min(fieldID) from @tblCrossEventFields where fieldID > @minFieldID;
				END
			END
		END

		-- recurring event
		IF @isRecurringEvent = 1 BEGIN
			SELECT @createdFromEventID = eventID, @lockTimeZoneID = lockTimeZoneID 
			FROM dbo.ev_events
			WHERE siteID = @siteID
			AND [status] = 'A'
			AND reportCode = @RecurringSeriesCode;

			select @recurrenceOrder = isnull((select columnValueInteger from #tmpEVQueueData where columnname = 'MCRecurrenceOrder'),0);

			IF @createdFromEventID IS NOT NULL AND @recurrenceOrder > 0 BEGIN
				SELECT @recurringSeriesID = seriesID, @recurrenceAFID = afID
				FROM dbo.ev_recurringSeries
				WHERE createdFromEventID = @createdFromEventID
				AND siteID = @siteID;

				SELECT @datePart = [datePart], @dateNum = dateNum, @adjustTerm = adjustTerm, @nextWeekday = nextWeekday, @weekNumber = weekNumber
				FROM dbo.af_advanceFormulas
				WHERE AFID = @recurrenceAFID
				AND siteID = @siteID;

				-- update seriesID
				UPDATE dbo.ev_events
				SET recurringSeriesID = @recurringSeriesID
				WHERE eventID = @MCEventID;

				-- update event times
				UPDATE dbo.ev_events 
				SET defaultTimeID = NULL, 
					lockedTimeID = NULL,
					lockTimeZoneID = @lockTimeZoneID
				WHERE eventID = @MCEventID;
				
				DELETE FROM dbo.ev_times 
				WHERE eventID = @MCEventID;
				
				INSERT INTO dbo.ev_times (eventID, timeZoneID, startTime, endTime)
				SELECT @MCEventID, timeZoneID, dbo.fn_af_getAFDate(startTime, @datePart, @dateNum * @recurrenceOrder, @adjustTerm, @nextWeekday, @weekNumber),
					dbo.fn_af_getAFDate(endTime, @datePart, @dateNum * @recurrenceOrder, @adjustTerm, @nextWeekday, @weekNumber)
				FROM dbo.ev_times
				WHERE eventID = @createdFromEventID;

				SELECT @defaultTimeID = timeID
				FROM dbo.ev_times
				WHERE eventID = @MCEventID
				AND timeZoneID = @defaultTimeZoneID;

				IF @lockTimeZoneID IS NOT NULL
					SELECT @lockedTimeID = timeID
					FROM dbo.ev_times
					WHERE eventID = @MCEventID
					AND timeZoneID = @lockTimeZoneID;

				UPDATE dbo.ev_events 
				SET defaultTimeID = @defaultTimeID, 
					lockedTimeID = @lockedTimeID
				WHERE eventID = @MCEventID;
			END
		END
	COMMIT TRAN;

	-- update status
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='readyToNotify';

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
