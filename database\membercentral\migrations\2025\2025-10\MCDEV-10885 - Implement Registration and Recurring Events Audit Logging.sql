USE membercentral
GO

ALTER PROC dbo.ev_queueCreatingRecurringEvents 
@siteID int,
@createdFromEventID int,
@afID int,
@endDate datetime,
@recordedByMemberID int,
@recurringEventsImportResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpRecurringEvents') IS NOT NULL
		DROP TABLE #tmpRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	
	-- bit cols defined as varchar for import validation
	CREATE TABLE #mc_EvImport (rowID int, Calendar varchar(100), EventTitle varchar(200), EventSubTitle varchar(200), InternalNotes varchar(max), EventCode varchar(15) NOT NULL, EventCategory varchar(max), 
		EventStart datetime, EventEnd datetime, EventHidden varchar(10), EventAllDay varchar(10), EventDescription varchar(max), ContactTitle varchar(200), 
		Contact varchar(max), ContactInclude varchar(max), LocationTitle varchar(200), Location varchar(max), LocationInclude varchar(max), 
		CancellationTitle varchar(200), Cancellation varchar(max), CancellationInclude varchar(max), TravelTitle varchar(200), Travel varchar(max), TravelInclude varchar(max), 
		InformationTitle varchar(200), Information varchar(max), RecurringSeriesCode varchar(15), RegistrationReplyEmail varchar(400));
	CREATE TABLE #tmpRecurringEvents (rowID int PRIMARY KEY IDENTITY(1,1), createdFromEventID int, eventCode varchar(15), startDate datetime, endDate datetime);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

	-- Audit logging variables
	DECLARE @orgID int, @calendarID int, @eventTitle varchar(200), @formulaDesc varchar(200), @msgjson varchar(max);

	DECLARE @seriesID int, @eventStart datetime, @eventEnd datetime, @swcfList varchar(max), 
		@fullsql varchar(max), @tmpSuffix varchar(10), @datePart varchar(20), @dateNum int, @adjustTerm varchar(12), 
		@nextWeekday int, @weekNumber varchar(4), @eventStartDate datetime, @eventEndDate datetime, @minRowID int,
		@eventCode varchar(15), @calendarPageName varchar(50), @categoryIDList varchar(200), @categoryList varchar(max);

	SET @tmpSuffix = CAST(@createdFromEventID AS varchar(10));

	-- Get audit data for recurring events logging
	SELECT @orgID = s.orgID, @calendarID = ce.calendarID, @eventTitle = eventcontent.contentTitle
	FROM dbo.sites s
	INNER JOIN dbo.ev_events e ON e.siteID = s.siteID
	INNER JOIN dbo.ev_calendarEvents ce ON ce.sourceEventID = e.eventID AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.cms_contentLanguages AS eventContent ON eventContent.contentID = e.eventContentID 
		AND eventContent.languageID = 1
	WHERE e.eventID = @createdFromEventID;

	-- Get advance formula description for audit message
	SELECT @formulaDesc = afName FROM dbo.af_advanceFormulas WHERE afID = @afID;

	SELECT @seriesID = seriesID
	FROM dbo.ev_recurringSeries
	WHERE createdFromEventID = @createdFromEventID
	AND siteID = @siteID;

	IF @seriesID IS NOT NULL
		GOTO on_done;

	SET @endDate = DATEADD(MS,-3,DATEADD(DAY,1,@endDate));

	SELECT @eventStartDate = et.startTime, @eventEndDate = et.endTime, @calendarPageName = aip.pageName,
		@categoryIDList = cat.categoryIDList
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
		AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars AS c ON c.siteID = @siteID
		AND c.calendarID = ce.calendarID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = c.applicationInstanceID
	CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,c.applicationInstanceID) as aip
	LEFT OUTER JOIN dbo.cache_calendarEventsCategoryIDList AS cat ON cat.eventID = e.eventID 
		AND cat.calendarID = ce.calendarID
	WHERE e.eventID = @createdFromEventID
	AND e.siteID = @siteID
	AND aip.applicationSiteResourceID = ai.siteResourceID
	AND aip.applicationSiteResourceType = 'Events';

	IF CAST(@endDate AS DATE) > CAST(DATEADD(YEAR,2,@eventStartDate) AS DATE)
		RAISERROR('Invalid Recurring End Date.', 16, 1);

	SELECT @datePart = [datePart], @dateNum = dateNum, @adjustTerm = adjustTerm, @nextWeekday = nextWeekday, @weekNumber = weekNumber
	FROM dbo.af_advanceFormulas
	WHERE AFID = @afID
	AND siteID = @siteID;

	WITH evDates AS (
		SELECT dbo.fn_af_getAFDate(@eventStartDate,@datePart,@dateNum,@adjustTerm,@nextWeekday,@weekNumber) AS startDate, 
			dbo.fn_af_getAFDate(@eventEndDate,@datePart,@dateNum,@adjustTerm,@nextWeekday,@weekNumber) AS endDate,
			1 AS rowNum
		UNION ALL
		SELECT startDate, endDate, rowNum
		FROM (
			SELECT dbo.fn_af_getAFDate(@eventStartDate,@datePart,@dateNum * (rowNum + 1),@adjustTerm,@nextWeekday,@weekNumber) AS startDate, 
				dbo.fn_af_getAFDate(@eventEndDate,@datePart,@dateNum * (rowNum + 1),@adjustTerm,@nextWeekday,@weekNumber) AS endDate,
				rowNum + 1 AS rowNum
			FROM evDates
		) tmp
		WHERE startDate <= @endDate
	)
	INSERT INTO #tmpRecurringEvents (createdFromEventID, startDate, endDate)
	SELECT @createdFromEventID, startDate, endDate
	FROM evDates
	OPTION (MAXRECURSION 300);

	SELECT @minRowID = MIN(rowID) FROM #tmpRecurringEvents;
	WHILE @minRowID IS NOT NULL BEGIN
		SET @eventCode = NULL;

		EXEC dbo.getUniqueCode @uniqueCode=@eventCode OUTPUT;

		UPDATE #tmpRecurringEvents
		SET eventCode = @eventCode
		WHERE rowID = @minRowID;

		SELECT @minRowID = MIN(rowID) FROM #tmpRecurringEvents WHERE rowID > @minRowID;
	END

	SELECT @categoryList = STRING_AGG(evCat.category,'|')
	FROM dbo.fn_IntListToTable(@categoryIDList,',') as tmpCat
	INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

	SET @categoryList = ISNULL(@categoryList,'');

	-- event custom fields	
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);
	
	INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
	SELECT siteResourceID, 'CrossEvent'
	FROM dbo.ev_events
	WHERE eventID = @createdFromEventID
	AND siteID = @siteID;

	EXEC dbo.cf_getFieldData;

	SELECT e.eventID, replace(f.fieldReference,',','') as fieldReference, 
		CASE WHEN ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX') THEN REPLACE(fd.fieldValue,', ', '|') ELSE fd.fieldValue END AS answer
	INTO #tmpSWCF
	FROM #tmp_CF_FieldData AS fd
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
	INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
	INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID;

	-- event custom fields pivoted
	set @swcfList = '';
	select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(fieldReference) from #tmpSWCF group by fieldReference;
	IF left(@swcfList,1) = ','
		set @swcfList = right(@swcfList,len(@swcfList)-1);
	IF len(@swcfList) > 0 BEGIN
		-- add swcf cols to import table
		select @fullsql = COALESCE(@fullsql, '') + 'ALTER TABLE #mc_EvImport ADD ' + quoteName(fieldReference)  + ' varchar(max);'
		from #tmpSWCF 
		group by fieldReference;

		EXEC(@fullsql);

		set @fullsql = '
			select * 
			into ##tmpSWCF'+@tmpSuffix+'
			from (
				select eventID, fieldReference, answer
				from #tmpSWCF
			) as cf
			PIVOT (min(answer) for fieldReference in (' + @swcfList + ')) as p ';
		EXEC(@fullsql);
	END
	ELSE
		EXEC('SELECT eventID INTO ##tmpSWCF'+@tmpSuffix+' FROM #tmpSWCF WHERE 0=1');

	SET @fullsql = 'SELECT ROW_NUMBER() OVER (ORDER BY tmp.startDate) AS rowID, '''+@calendarPageName+''', eventcontent.contentTitle, ev.eventSubTitle, ev.internalNotes, tmp.eventCode, '''+@categoryList+''', tmp.startDate, tmp.endDate, 
		ISNULL(ev.hiddenFromCalendar,0), ev.isAllDayEvent, eventcontent.rawContent, contactcontent.contentTitle, contactcontent.rawContent, ISNULL(ev.emailContactContent,0),
		locationcontent.contentTitle, locationcontent.rawContent, ISNULL(ev.emailLocationContent,0), cancelcontent.contentTitle, cancelcontent.rawContent, 
		ISNULL(ev.emailCancelContent,0), travelcontent.contentTitle, travelcontent.rawContent, ISNULL(ev.emailTravelContent,0), 
		informationcontent.contentTitle, informationcontent.rawContent, ev.reportCode, '''' AS RegistrationReplyEmail';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + ', swcf.' + replace(@swcfList,',',',swcf.');
	SET @fullsql = @fullsql + '
		FROM #tmpRecurringEvents AS tmp
		INNER JOIN dbo.ev_events AS ev ON ev.siteID = '+CAST(@siteID AS varchar(10))+' AND ev.eventID = tmp.createdFromEventID
		CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) AS eventcontent
		CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) AS locationcontent
		CROSS APPLY dbo.fn_getContent(ev.travelcontentID,1) AS travelcontent
		CROSS APPLY dbo.fn_getContent(ev.contactcontentID,1) AS contactcontent
		CROSS APPLY dbo.fn_getContent(ev.cancellationPolicycontentID,1) AS cancelcontent
		CROSS APPLY dbo.fn_getContent(ev.informationContentID,1) AS informationcontent';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + '
			LEFT OUTER JOIN ##tmpSWCF'+@tmpSuffix+' AS swcf ON swcf.eventID = ev.eventID';

	
	INSERT INTO #mc_EvImport
	EXEC(@fullsql);

	-- drop global table
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	BEGIN TRAN;
		INSERT INTO dbo.ev_recurringSeries (siteID, createdFromEventID, afID)
		VALUES (@siteID, @createdFromEventID, @afID);

		SET @seriesID = SCOPE_IDENTITY();

		UPDATE dbo.ev_events
		SET recurringSeriesID = @seriesID
		WHERE eventID = @createdFromEventID;

		-- Audit logging for recurring series creation
		IF @calendarID IS NOT NULL AND @orgID IS NOT NULL BEGIN
			DECLARE @eventCount int, @evKeyMapJSON varchar(100);
			SELECT @eventCount = COUNT(*) FROM #tmpRecurringEvents;

			SET @evKeyMapJSON = '{ "EVENTID":'+CAST(@createdFromEventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
			SET @msgjson = STRING_ESCAPE('Recurring event series created for event [' + @eventTitle + '] with pattern [' + ISNULL(@formulaDesc, 'Custom') + '] ending [' + CONVERT(varchar(20), @endDate, 120) + '], creating [' + CAST(@eventCount AS varchar(10)) + '] events queued for import.', 'json');

			EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN;

	-- queue recurring events import
	EXEC dbo.ev_importEvents @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction='s', @importResult=@recurringEventsImportResult OUTPUT;

	on_done:

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpRecurringEvents') IS NOT NULL
		DROP TABLE #tmpRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ev_updateUpcomingRecurringEvents 
@siteID int,
@copyFromEventID int,
@recordedByMemberID int,
@recurringEventsImportResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	
	-- bit cols defined as varchar for import validation
	CREATE TABLE #mc_EvImport (rowID int, Calendar varchar(100), EventTitle varchar(200), EventSubTitle varchar(200), InternalNotes varchar(max), EventCode varchar(15) NOT NULL, EventCategory varchar(max), 
		EventStart datetime, EventEnd datetime, EventHidden varchar(10), EventAllDay varchar(10), EventDescription varchar(max), ContactTitle varchar(200), 
		Contact varchar(max), ContactInclude varchar(max), LocationTitle varchar(200), Location varchar(max), LocationInclude varchar(max), 
		CancellationTitle varchar(200), Cancellation varchar(max), CancellationInclude varchar(max), TravelTitle varchar(200), Travel varchar(max), TravelInclude varchar(max), 
		InformationTitle varchar(200), Information varchar(max), RegistrationReplyEmail varchar(400));
	CREATE TABLE #tmpExistingRecurringEvents (eventID int PRIMARY KEY, siteResourceID int, eventCode varchar(15), startDate datetime, endDate datetime);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

	-- Audit logging variables
	DECLARE @orgID int, @calendarID int, @eventTitle varchar(200), @msgjson varchar(max);

	DECLARE @recurringSeriesID int, @swcfList varchar(max), @fullsql varchar(max), @tmpSuffix varchar(36), 
		@eventStartDate datetime, @eventEndDate datetime, @calendarPageName varchar(50), @categoryIDList varchar(200), 
		@categoryList varchar(max), @defaultTimeZoneID int, @eventSiteResourceID int;

	SET @tmpSuffix = REPLACE(CAST(NEWID() AS varchar(36)),'-','');

	SELECT @defaultTimeZoneID = defaultTimeZoneID 
	FROM dbo.sites 
	WHERE siteID = @siteID;
	
	-- Get audit data for recurring events update logging
	SELECT @orgID = s.orgID, @calendarID = ce.calendarID, @eventTitle = eventcontent.contentTitle
	FROM dbo.sites s
	INNER JOIN dbo.ev_events e ON e.siteID = s.siteID
	INNER JOIN dbo.ev_calendarEvents ce ON ce.sourceEventID = e.eventID AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.cms_contentLanguages AS eventContent ON eventContent.contentID = e.eventContentID 
		AND eventContent.languageID = 1
	WHERE e.eventID = @copyFromEventID;

	SELECT @recurringSeriesID = e.recurringSeriesID, @eventStartDate = et.startTime, @eventEndDate = et.endTime, 
		@calendarPageName = aip.pageName, @categoryIDList = cat.categoryIDList, @eventSiteResourceID = e.siteResourceID
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
		AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars AS c ON c.siteID = @siteID
		AND c.calendarID = ce.calendarID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = c.applicationInstanceID
	CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,c.applicationInstanceID) as aip
	LEFT OUTER JOIN dbo.cache_calendarEventsCategoryIDList AS cat ON cat.eventID = e.eventID 
		AND cat.calendarID = ce.calendarID
	WHERE e.eventID = @copyFromEventID
	AND e.siteID = @siteID
	AND aip.applicationSiteResourceID = ai.siteResourceID
	AND aip.applicationSiteResourceType = 'Events';

	-- site doesn't support recurring events or not an recurring event
	IF @recurringSeriesID IS NULL
		GOTO on_done;

	SELECT @categoryList = STRING_AGG(evCat.category,'|')
	FROM dbo.fn_IntListToTable(@categoryIDList,',') as tmpCat
	INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

	SET @categoryList = ISNULL(@categoryList,'');

	-- upcoming recurring events
	INSERT INTO #tmpExistingRecurringEvents (eventID, siteResourceID, eventCode, startDate, endDate)
	SELECT e.eventID, e.siteResourceID, e.reportCode, et.startTime, et.endTime
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	WHERE e.recurringSeriesID = @recurringSeriesID
	AND e.siteID = @siteID
	AND e.[status] IN ('A','I')
	AND et.startTime > @eventStartDate;

	IF @@ROWCOUNT = 0
		GOTO on_done;
	
	-- event custom fields	
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);
	
	INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
	SELECT siteResourceID, 'CrossEvent'
	FROM dbo.ev_events
	WHERE eventID = @copyFromEventID
	AND siteID = @siteID;

	EXEC dbo.cf_getFieldData;

	SELECT e.eventID, replace(f.fieldReference,',','') as fieldReference, 
		CASE WHEN ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX') THEN REPLACE(fd.fieldValue,', ', '|') ELSE fd.fieldValue END AS answer
	INTO #tmpSWCF
	FROM #tmp_CF_FieldData AS fd
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
	INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
	INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID;

	-- event custom fields pivoted
	set @swcfList = '';
	select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(fieldReference) from #tmpSWCF group by fieldReference;
	IF left(@swcfList,1) = ','
		set @swcfList = right(@swcfList,len(@swcfList)-1);
	IF len(@swcfList) > 0 BEGIN
		-- add swcf cols to import table
		select @fullsql = COALESCE(@fullsql, '') + 'ALTER TABLE #mc_EvImport ADD ' + quoteName(fieldReference)  + ' varchar(max);'
		from #tmpSWCF 
		group by fieldReference;

		EXEC(@fullsql);

		set @fullsql = '
			select * 
			into ##tmpSWCF'+@tmpSuffix+'
			from (
				select eventID, fieldReference, answer
				from #tmpSWCF
			) as cf
			PIVOT (min(answer) for fieldReference in (' + @swcfList + ')) as p ';
		EXEC(@fullsql);
	END
	ELSE
		EXEC('SELECT eventID INTO ##tmpSWCF'+@tmpSuffix+' FROM #tmpSWCF WHERE 0=1');

	-- prep final data for import
	SET @fullsql = 'SELECT DISTINCT ROW_NUMBER() OVER (ORDER BY tmp.startDate) AS rowID, '''+@calendarPageName+''', eventcontent.contentTitle, ev.eventSubTitle, ev.internalNotes, tmp.eventCode, '''+@categoryList+''', 
		tmp.startDate, tmp.endDate, ISNULL(ev.hiddenFromCalendar,0), ev.isAllDayEvent, eventcontent.rawContent, contactcontent.contentTitle, contactcontent.rawContent, 
		ISNULL(ev.emailContactContent,0), locationcontent.contentTitle, locationcontent.rawContent, ISNULL(ev.emailLocationContent,0), cancelcontent.contentTitle, cancelcontent.rawContent, 
		ISNULL(ev.emailCancelContent,0), travelcontent.contentTitle, travelcontent.rawContent, ISNULL(ev.emailTravelContent,0), 
		informationcontent.contentTitle, informationcontent.rawContent, '''' AS RegistrationReplyEmail';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + ', swcf.' + replace(@swcfList,',',',swcf.');
	SET @fullsql = @fullsql + '
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.ev_events AS ev ON ev.siteID = '+CAST(@siteID AS varchar(10))+' AND ev.eventID = '+CAST(@copyFromEventID AS varchar(10))+'
		CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) AS eventcontent
		CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) AS locationcontent
		CROSS APPLY dbo.fn_getContent(ev.travelcontentID,1) AS travelcontent
		CROSS APPLY dbo.fn_getContent(ev.contactcontentID,1) AS contactcontent
		CROSS APPLY dbo.fn_getContent(ev.cancellationPolicycontentID,1) AS cancelcontent
		CROSS APPLY dbo.fn_getContent(ev.informationContentID,1) AS informationcontent';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + '
			LEFT OUTER JOIN ##tmpSWCF'+@tmpSuffix+' AS swcf ON swcf.eventID = ev.eventID';

	INSERT INTO #mc_EvImport
	EXEC(@fullsql);

	-- drop global table
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	-- Audit logging before updating recurring events
	IF @calendarID IS NOT NULL AND @orgID IS NOT NULL BEGIN
		DECLARE @eventCount int, @evKeyMapJSON varchar(100);
		SELECT @eventCount = COUNT(*) FROM #tmpExistingRecurringEvents;

		SET @evKeyMapJSON = '{ "EVENTID":'+CAST(@copyFromEventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('Upcoming recurring events updated for event [' + @eventTitle + '], updating [' + CAST(@eventCount AS varchar(10)) + '] future events in the series.', 'json');

		EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@recordedByMemberID;
	END

	-- queue recurring events import
	EXEC dbo.ev_importEvents @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction='o', @importResult=@recurringEventsImportResult OUTPUT;

	IF @recurringEventsImportResult.value('count(/import/errors/error)','int') = 0 BEGIN
		-- update asset categories
		DELETE csr
		FROM dbo.cms_categorySiteResources AS csr
		INNER JOIN #tmpExistingRecurringEvents AS tmp ON tmp.siteResourceID = csr.siteResourceID;

		INSERT INTO dbo.cms_categorySiteResources (categoryID, siteResourceID)
		SELECT DISTINCT csr.categoryID, tmp.siteResourceID
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.cms_categorySiteResources AS csr ON csr.siteResourceID = @eventSiteResourceID;
	END

	on_done:

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ev_copyEvent
@eventid int,
@incSiteEventCustomField bit,
@incSponsor bit,
@incCreditOffered bit,
@incRate bit,
@incCustomField bit,
@incTickets bit,
@copiedByMemberID int,
@newEventID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpNewEventRates') IS NOT NULL 
		DROP TABLE #tmpNewEventRates;
	CREATE TABLE #tmpNewEventRates (oldRateID int not null, newRateID int not null);

	DECLARE @siteID int, @eventTypeID int, @lockTimeZoneID int, @defaultTimeID int, @timeZoneID int, @newTimeID int,@isAllDayEvent bit, @hiddenFromCalendar bit,
		@altRegistrationURL varchar(300), @GLAccountID int, @calendarID int, @status char(1), @reportCode varchar(15),
		@eventSubTitle varchar(200), @internalNotes varchar(max), @eventContentID int, @contactContentID int, @locationContentID int, @cancelContentID int, 
		@travelContentID int, @informationContentID int, @neweventContentID int, @newcontactContentID int, @newlocationContentID int, 
		@newcancelContentID int, @newtravelContentID int, @newinformationContentID int, @languageID int, @isHTML bit, 
		@contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max), @emailContactContent bit, 
		@emailLocationContent bit, @emailCancelContent bit, @emailTravelContent bit, @registrationID int, @registrationTypeID int, 
		@startDate datetime, @endDate datetime, @registrantCap int, @ReplyToEmail varchar(200), @notifyEmail varchar(200), 
		@isPriceBasedOnActual bit, @bulkCountByRate bit, @newregistrationID int, @expirationContentID int, @siteResourceID int,
		@registrantCapContentID int, @newexpirationContentID int, @newregistrantCapContentID int, @minofferingID int, @srr_rightsID int, 
		@newofferingID int, @minFieldID int, @newFieldID int, @showCredit bit, @isOnlineMeeting bit, @onlineEmbedCode varchar(max), 
		@onlineEmbedOverrideLink varchar(400), @onlineEnterStartTime datetime, @onlineEnterEndTime datetime, @newEventSiteResourceID int,
		@srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_include bit, @srr_inheritedRightsResourceID int, 
		@srr_inheritedRightsFunctionID int, @scheduleID int, @newScheduleID int, @valueID int, @newValueID int,
		@registrantEditDeadlineContentID int, @newregistrantEditDeadlineContentID int, @registrantEditAllowed bit, @registrantEditDeadline datetime,
		@registrantEditRefundContentID int, @newregistrantEditRefundContentID int, @evRegFieldUsageID int;
	SET @newEventID = 0;

	-- get the event we are copying
	SELECT @siteID=siteID, @eventTypeID=eventTypeID, 
		@lockTimeZoneID=lockTimeZoneID, @defaultTimeID=defaultTimeID, @isAllDayEvent=isAllDayEvent, @altRegistrationURL=altRegistrationURL,
		@GLAccountID=GLAccountID, @status=status, @eventSubTitle=eventSubTitle, @internalNotes=internalNotes,
		@emailContactContent=emailContactContent, @emailLocationContent=emailLocationContent,
		@emailCancelContent=emailCancelContent, @emailTravelContent=emailTravelContent,
		@hiddenFromCalendar=hiddenFromCalendar, @eventContentID=eventContentID, @contactContentID=contactContentID, 
		@locationContentID=locationContentID, @siteResourceID=siteResourceID, @cancelContentID=cancellationPolicyContentID, 
		@travelContentID=travelContentID, @informationContentID=informationContentID
	FROM dbo.ev_events
	WHERE eventID = @eventID;

	SELECT TOP 1 @calendarID = calendarID
	FROM dbo.ev_calendarEvents
	WHERE calendarID = sourceCalendarID
	AND sourceEventID = @eventID;

	SELECT @evRegFieldUsageID = dbo.fn_cf_getUsageID('Event','Registrant',NULL);
	
	-- generate unique report code. 
	-- it will just be temporary since we'll immediately change it to be have the eventID
	EXEC dbo.getUniqueCode @uniqueCode=@reportCode OUTPUT;
	
	BEGIN TRAN
		-- create event
		EXEC dbo.ev_createEvent @siteID=@siteID, @calendarID=@calendarID, @eventTypeID=@eventTypeID, 
			@enteredByMemberID=@copiedByMemberID, @eventSubTitle=@eventSubTitle, @lockTimeZoneID=@lockTimeZoneID, 
			@isAllDayEvent=@isAllDayEvent, @altRegistrationURL=@altRegistrationURL, @status=@status, 
			@reportCode=@reportCode, @hiddenFromCalendar=@hiddenFromCalendar, @emailContactContent=@emailContactContent, 
			@emailLocationContent=@emailLocationContent, @emailCancelContent=@emailCancelContent, 
			@emailTravelContent=@emailTravelContent, @eventID=@newEventID OUTPUT;
		
		-- copy event times
		INSERT INTO dbo.ev_times (eventid, timeZoneID, startTime, endTime)
		SELECT @newEventID, timeZoneID, startTime, endTime
		FROM dbo.ev_times
		WHERE eventid = @eventID;
		
		SELECT @timeZoneID = timeZoneID FROM dbo.ev_times WHERE timeID = @defaultTimeID AND eventid = @eventID;
		SELECT @newTimeID = timeID FROM dbo.ev_times WHERE timeZoneID = @timeZoneID AND eventid = @newEventID;

		update dbo.ev_events
		set GLAccountID = @GLAccountID,
			internalNotes = @internalNotes,
			reportCode = 'EVC' + cast(@newEventID as varchar(10)),
			[status] = 'I',
			defaultTimeID = @newTimeID
		where eventID = @newEventID;

		select @neweventContentID=eventContentID, @newcontactContentID=contactContentID, @newlocationContentID=locationContentID, @newEventSiteResourceID=siteResourceID,
			@newcancelContentID=cancellationPolicyContentID, @newtravelContentID=travelContentID, @newinformationContentID=informationContentID
		FROM dbo.ev_events
		where eventID = @newEventID;

		-- copy sub-event links
		insert into dbo.ev_subEvents (parentEventID, eventID, forceSelect, sendStaffConfirmation, sendRegConfirmation)
		select parentEventID, @newEventID, forceSelect, sendStaffConfirmation, sendRegConfirmation
		from dbo.ev_subEvents
		where eventid = @eventid;

		-- copy event category
		INSERT INTO dbo.ev_eventcategories (eventID, categoryID, categoryOrder)
		select @newEventID, categoryID, categoryOrder
		from dbo.ev_eventcategories
		where eventID = @eventID;

		-- copy content objects
		select @languageID=languageID, @isHTML=isHTML, @contentTitle='Copy of ' + contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@eventContentID,1);
		EXEC dbo.cms_updateContent @contentID=@neweventContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

		select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@contactContentID,1);
		EXEC dbo.cms_updateContent @contentID=@newcontactContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

		select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@locationContentID,1);
		EXEC dbo.cms_updateContent @contentID=@newlocationContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

		select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@cancelContentID,1);
		EXEC dbo.cms_updateContent @contentID=@newcancelContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

		select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@travelContentID,1);
		EXEC dbo.cms_updateContent @contentID=@newtravelContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

		select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@informationContentID,1);
		EXEC dbo.cms_updateContent @contentID=@newinformationContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

		-- copy sponsors
		if @incSponsor = 1
			insert into dbo.sponsorsUsage (sponsorID, referenceType, referenceID, sponsorOrder)
			select sponsorID, referenceType, @newEventID, sponsorOrder
			from dbo.sponsorsUsage
			where referenceType = 'Events'
			and referenceID = @eventID;

		-- copy systemwide event custom field values
		if @incSiteEventCustomField = 1
			insert into dbo.cf_fieldData (fieldID, itemType, itemID, valueID, amount)
			select fieldID, itemType, @newEventSiteResourceID, valueID, amount
			from dbo.cf_fieldData
			where itemID = @siteResourceID
			and itemType = 'CrossEvent';

		-- does orig event have registration?
		SELECT @registrationID=registrationid, @registrationTypeID=registrationTypeID, @startDate=startdate, 
			@endDate=endDate, @registrantCap=registrantCap, @ReplyToEmail=ReplyToEmail, @notifyEmail=notifyEmail, 
			@isPriceBasedOnActual=isPriceBasedOnActual, @bulkCountByRate=bulkCountByRate, @expirationContentID=expirationContentID, 
			@registrantCapContentID=registrantCapContentID, @isOnlineMeeting=isOnlineMeeting, @onlineEmbedCode=onlineEmbedCode,
			@onlineEmbedOverrideLink=onlineEmbedOverrideLink, @onlineEnterStartTime=onlineEnterStartTime, @onlineEnterEndTime=onlineEnterEndTime,
			@showCredit=showCredit, @registrantEditAllowed=registrantEditAllowed, @registrantEditDeadline=registrantEditDeadline, 
			@registrantEditDeadlineContentID=registrantEditDeadlineContentID, @registrantEditRefundContentID=registrantEditRefundContentID
		from dbo.ev_registration
		where eventID = @eventID
		and [status] <> 'D';

		IF @registrationID is not null BEGIN
			-- insert registration
			EXEC dbo.ev_createRegistration @eventID=@newEventID, @registrationTypeID=@registrationTypeID, 
					@startDate=@startDate, @endDate=@endDate, @registrantCap=@registrantCap, @ReplyToEmail=@ReplyToEmail,
					@notifyEmail=@notifyEmail, @isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate,@enteredByMemberID=@copiedByMemberID,
					@registrationID=@newregistrationID OUTPUT;

			select @newexpirationContentID=expirationContentID, @newregistrantCapContentID=registrantCapContentID, 
				@newregistrantEditDeadlineContentID=registrantEditDeadlineContentID, @newregistrantEditRefundContentID=registrantEditRefundContentID
			FROM dbo.ev_registration
			where registrationID = @newregistrationID;

			-- copy content objects
			select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@expirationContentID,1);
			EXEC dbo.cms_updateContent @contentID=@newexpirationContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

			select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantCapContentID,1);
			EXEC dbo.cms_updateContent @contentID=@newregistrantCapContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

			select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantEditDeadlineContentID,1);
			EXEC dbo.cms_updateContent @contentID=@newregistrantEditDeadlineContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

			select @languageID=languageID, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantEditRefundContentID,1);
			EXEC dbo.cms_updateContent @contentID=@newregistrantEditRefundContentID, @languageID=@languageID, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID;

			-- if reg type
			IF @registrationTypeID = 1 BEGIN
				-- other registration fields
				update dbo.ev_registration
				set isOnlineMeeting = @isOnlineMeeting,
					onlineEmbedCode = @onlineEmbedCode,
					onlineEmbedOverrideLink = @onlineEmbedOverrideLink,
					onlineEnterStartTime = @onlineEnterStartTime,
					onlineEnterEndTime = @onlineEnterEndTime,
					showCredit = @showCredit,
					registrantEditAllowed = @registrantEditAllowed,
					registrantEditDeadline = @registrantEditDeadline
				where registrationID = @newregistrationID;

				-- merchant profiles
				insert into dbo.ev_registrationMerchantProfiles (registrationID, profileID)
				select @newregistrationID, rmp.profileID
				from dbo.ev_registrationMerchantProfiles as rmp
				inner join dbo.mp_profiles as mp on mp.profileID = rmp.profileID
				inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
				where rmp.registrationID = @registrationID
				and mp.status = 'A'
				and mp.allowPayments = 1
				and g.isActive = 1;

				-- credit offered
				if @incCreditOffered = 1 begin
					select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID;
					while @minofferingID is not null begin
						INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, eventID)
						SELECT ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, @newEventID
						FROM dbo.crd_offerings
						WHERE offeringID = @minofferingID;
							SELECT @newofferingID = SCOPE_IDENTITY()

						INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
						SELECT @newofferingID, ASTID, creditValue
						FROM dbo.crd_offeringTypes
						WHERE offeringID = @minofferingID;

						select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID and offeringID > @minofferingID;
					end
				end

				-- delete the auto-created price schedule from ev_createRegistration
				delete from dbo.ev_priceSchedule
				where registrationID = @newregistrationID;

				-- copy schedule entries
				insert into dbo.ev_priceSchedule (registrationID, rangeName, startDate, endDate)
				select @newregistrationID as registrationID, rangeName, startDate, endDate
				from dbo.ev_priceSchedule
				where registrationID = @registrationID;

				-- copy rates
				if @incRate = 1 BEGIN
					INSERT INTO #tmpNewEventRates (oldRateID, newRateID)
					EXEC dbo.ev_copyRates @siteID=@siteID, @copyFromRegistrationID=@registrationID, @copyToRegistrationID=@newregistrationID, @incScheduleEntries=1;
				END

				-- custom questions
				if @incCustomField = 1 begin
					-- field groupings				
					insert into dbo.cf_fieldsGrouping (fieldGrouping, fieldGroupingDesc, fieldControllingSiteResourceID, fieldUsageID, fieldDetailID, fieldGroupingOrder)
					select fieldGrouping, fieldGroupingDesc, @newEventSiteResourceID, fieldUsageID, NULL, fieldGroupingOrder
					from dbo.cf_fieldsGrouping
					where fieldControllingSiteResourceID = @siteResourceID
					and fieldUsageID = @evRegFieldUsageID;
					
					select @minFieldID = min(f.fieldID) 
						from dbo.cf_fields as f
						inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
						where f.controllingSiteResourceID = @siteResourceID 
						and fu.parentUsageID = @evRegFieldUsageID
						and f.isActive = 1;				
						
					WHILE @minFieldID is not null BEGIN
						select @newFieldID=null, @scheduleID=null, @valueID=null;		

						insert into dbo.cf_fields (fieldGroupingID, controllingSiteResourceID, usageID, detailID, fieldTypeID, fieldText, fieldReference, isRequired, requiredMsg,  
							adminOnly, displayOnly, autoFillRegistrant, amount, GLAccountID, inventory, uid, fieldOrder)
						select distinct fg.fieldGroupingID, @newEventSiteResourceID, f.usageID, f.detailID, f.fieldTypeID, f.fieldText, f.fieldReference, f.isRequired, f.requiredMsg, 
							f.adminOnly, f.displayOnly, f.autoFillRegistrant, f.amount, f.GLAccountID, f.inventory, newid(), f.fieldOrder
						from dbo.cf_fields as f
						left outer join dbo.cf_fieldsGrouping as fgOld on fgOld.fieldGroupingID = f.fieldGroupingID
						left outer join dbo.cf_fieldsGrouping as fg on fg.fieldControllingSiteResourceID = @newEventSiteResourceID
							and fg.fieldUsageID = @evRegFieldUsageID
							and fg.fieldGrouping = fgOld.fieldGrouping
						where f.fieldID = @minFieldID;
							SELECT @newFieldID = SCOPE_IDENTITY();

						select @scheduleID=min(fa.scheduleID)
						from dbo.cf_fields as f
						inner join dbo.cf_fieldAvailable_usage1 as fa on fa.fieldID = f.fieldID and f.controllingSiteResourceID = @siteResourceID
							and f.fieldID = @minFieldID
						inner join dbo.ev_priceSchedule as ep on ep.scheduleID = fa.scheduleID;

						while @scheduleID is not null begin
							select @newScheduleID=null;

							select @newScheduleID = ep_new.scheduleID
							from dbo.ev_priceSchedule as ep_new
							where registrationID = @newRegistrationID
							and exists(select 1 from dbo.ev_priceSchedule 
										where registrationID = @registrationID 
										and scheduleID = @scheduleID
										and rangeName = ep_new.rangeName
										and startDate = ep_new.startDate
										and endDate = ep_new.endDate);

							if @newScheduleID is not null begin
								insert into dbo.cf_fieldAvailable_usage1 (fieldID, scheduleID, amount)
								select @newFieldID, @newScheduleID, amount
								from dbo.cf_fieldAvailable_usage1 
								where fieldID = @minFieldID 
								and scheduleID = @scheduleID;
							end

							select @scheduleID=min(fa.scheduleID)
							from dbo.cf_fields as f
							inner join dbo.cf_fieldAvailable_usage1 as fa on fa.fieldID = f.fieldID and f.controllingSiteResourceID = @siteResourceID
								and f.fieldID = @minFieldID
							inner join dbo.ev_priceSchedule as ep on ep.scheduleID = fa.scheduleID
							where fa.scheduleID > @scheduleID;
						end

						select @valueID = min(valueID)
						from dbo.cf_fieldValues as fv
						inner join dbo.cf_fields as f on f.fieldID = fv.fieldID
						inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
						where fv.fieldID = @minFieldID
						and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX');

						while @valueID is not null begin
							select @newValueID=null, @scheduleID=null;

							insert into dbo.cf_fieldValues (fieldID, valueString, valueDecimal2, valueInteger, valueBit, valueDate, valueSiteResourceID, amount, inventory, valueOrder)
							select @newFieldID, valueString, valueDecimal2, valueInteger, valueBit, valueDate, valueSiteResourceID, amount, inventory, valueOrder
							from dbo.cf_fieldValues
							where fieldID = @minFieldID
							and valueID = @valueID;
								SELECT @newValueID = SCOPE_IDENTITY();

							select @scheduleID=min(fva.scheduleID)
							from dbo.cf_fieldValues as fv
							inner join dbo.cf_fieldValuesAvailable_usage1 as fva on fva.valueID = fv.valueID 
								and fv.fieldID = @minFieldID and fv.valueID = @valueID
							inner join dbo.ev_priceSchedule as ep on ep.scheduleID = fva.scheduleID;

							while @scheduleID is not null begin
								select @newScheduleID=null;

								select @newScheduleID = ep_new.scheduleID
								from dbo.ev_priceSchedule as ep_new
								where registrationID = @newRegistrationID
								and exists(select 1 from dbo.ev_priceSchedule 
											where registrationID = @registrationID 
											and scheduleID = @scheduleID
											and rangeName = ep_new.rangeName
											and startDate = ep_new.startDate
											and endDate = ep_new.endDate);
								
								if @newScheduleID is not null begin
									insert into dbo.cf_fieldValuesAvailable_usage1 (valueID, scheduleID, amount)
									select @newValueID, @newScheduleID, amount
									from dbo.cf_fieldValuesAvailable_usage1 
									where valueID = @valueID
									and scheduleID = @scheduleID;
								end

								select @scheduleID=min(fva.scheduleID)
								from dbo.cf_fieldValues as fv
								inner join dbo.cf_fieldValuesAvailable_usage1 as fva on fva.valueID = fv.valueID 
									and fv.fieldID = @minFieldID and fv.valueID = @valueID
								inner join dbo.ev_priceSchedule as ep on ep.scheduleID = fva.scheduleID
								where fva.scheduleID > @scheduleID;
							end

							select @valueID = min(valueID)
							from dbo.cf_fieldValues as fv
							inner join dbo.cf_fields as f on f.fieldID = fv.fieldID
							inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
							where fv.fieldID = @minFieldID
							and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
							and valueID > @valueID;
						end

						select @minFieldID = min(f.fieldID) 
						from dbo.cf_fields as f
						inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
						where f.controllingSiteResourceID = @siteResourceID 
						and fu.parentUsageID = @evRegFieldUsageID
						and f.isActive = 1
						and f.fieldID > @minFieldID;
					END
				end

				if @incTickets = 1
					EXEC dbo.ev_copyTickets @siteID=@siteID, @copyFromRegistrationID=@registrationID, @copyToRegistrationID=@newregistrationID, @incRate=@incRate, @incScheduleEntries=1;

			END

		END
		
		-- copy permissions	
		SET @srr_rightsID = null;
		SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and siteID = @siteID;
		WHILE @srr_rightsID IS NOT NULL BEGIN
			select @srr_inheritedRightsResourceID = null, @srr_inheritedRightsFunctionID = null;

			SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
				@srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
				@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
			FROM dbo.cms_siteResourceRights
			WHERE resourceRightsID = @srr_rightsID and siteID = @siteID;

			EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newEventSiteResourceID, @include=@srr_include, 
				@functionIDList=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @inheritedRightsResourceID=@srr_inheritedRightsResourceID, 
				@inheritedRightsFunctionID=@srr_inheritedRightsFunctionID;

			SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and siteID = @siteID and resourceRightsID > @srr_rightsID;
		END
		
	COMMIT TRAN;

	EXEC dbo.ev_refreshCalendarEventsCache @siteID=@siteID;
	EXEC dbo.ev_refreshCalendarEventsCategoryIDList @siteID=@siteID, @eventID=@newEventID;

	-- queue update search text
	EXEC dbo.ev_queueEventSearchText @siteID=@siteID, @eventID=@newEventID;

	IF OBJECT_ID('tempdb..#tmpNewEventRates') IS NOT NULL
		DROP TABLE #tmpNewEventRates;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

